import React from "react";
import { getSystemInfo } from "zmp-sdk";
import {
  AnimationRoutes,
  App,
  Route,
  Snac<PERSON><PERSON><PERSON><PERSON><PERSON>,
  ZMPRouter,
  useLocation,
} from "zmp-ui";
import { AppProps } from "zmp-ui/app";

import WelcomeScreen from "@/components/game/WelcomeScreen";
import ImageUploadScreen from "@/components/game/ImageUploadScreen";
import SurveyScreen from "@/components/game/SurveyScreen";
import ResultsScreen from "@/components/game/ResultsScreen";

interface GameState {
  sessionId: string;
  currentScreen: number;
  uploadedImages: File[];
  surveyResponses: any[];
  startTime: Date;
  completionTime?: Date;
}

const GameRoutes = () => {
  const location = useLocation();
  // Game state management
  const [gameState, setGameState] = React.useState<GameState>({
    sessionId: 'game_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
    currentScreen: 1,
    uploadedImages: [],
    surveyResponses: [],
    startTime: new Date()
  });

  // Load saved state from localStorage on mount
  React.useEffect(() => {
    const savedState = localStorage.getItem('gameState');
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState);
        setGameState({
          ...parsed,
          startTime: new Date(parsed.startTime),
          completionTime: parsed.completionTime ? new Date(parsed.completionTime) : undefined
        });
      } catch (error) {
        console.error('Error loading saved game state:', error);
      }
    }
  }, []);

  // Save state to localStorage whenever it changes
  React.useEffect(() => {
    localStorage.setItem('gameState', JSON.stringify(gameState));
  }, [gameState]);

  // Update current screen based on route
  React.useEffect(() => {
    const screenMap: { [key: string]: number } = {
      '/': 1,
      '/upload': 2,
      '/survey': 3,
      '/results': 4
    };

    const currentScreen = screenMap[location.pathname] || 1;
    setGameState(prev => ({ ...prev, currentScreen }));
  }, [location.pathname]);

  const handleGameStart = () => {
    setGameState(prev => ({
      ...prev,
      startTime: new Date(),
      currentScreen: 2
    }));
  };

  const handleImageUpload = (images: File[]) => {
    setGameState(prev => ({
      ...prev,
      uploadedImages: images
    }));
  };

  const handleSurveyComplete = (responses: any[]) => {
    setGameState(prev => ({
      ...prev,
      surveyResponses: responses,
      completionTime: new Date(),
      currentScreen: 4
    }));
  };

  const handleGameRestart = () => {
    const newState: GameState = {
      sessionId: 'game_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
      currentScreen: 1,
      uploadedImages: [],
      surveyResponses: [],
      startTime: new Date()
    };

    setGameState(newState);
    localStorage.removeItem('gameState');
  };

  return (
    <>
      {/* Global Progress Bar */}
      <div className="global-progress">
        <div
          className="progress-fill"
          style={{ width: `${(gameState.currentScreen / 4) * 100}%` }}
        />
      </div>

      <AnimationRoutes>
        <Route
          path="/"
          element={
            <WelcomeScreen onStart={handleGameStart} />
          }
        />
        <Route
          path="/upload"
          element={
            <ImageUploadScreen
              onImageUpload={handleImageUpload}
              uploadedImages={gameState.uploadedImages}
            />
          }
        />
        <Route
          path="/survey"
          element={
            <SurveyScreen
              onSurveyComplete={handleSurveyComplete}
            />
          }
        />
        <Route
          path="/results"
          element={
            <ResultsScreen
              uploadedImages={gameState.uploadedImages}
              surveyResponses={gameState.surveyResponses}
              onRestart={handleGameRestart}
            />
          }
        />
      </AnimationRoutes>
    </>
  );
};

const Layout = () => {
  return (
    <App theme={getSystemInfo().zaloTheme as AppProps["theme"]}>
      <SnackbarProvider>
        <ZMPRouter>
          <GameRoutes />
        </ZMPRouter>
      </SnackbarProvider>
    </App>
  );
};
export default Layout;
