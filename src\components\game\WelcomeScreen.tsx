import React from 'react';
import { <PERSON><PERSON>, Box, Text, Page } from 'zmp-ui';
import { useNavigate } from 'zmp-ui';
import bgImage from '@/static/bg.png';
import selectZoneImage from '@/static/select-zone.png';

interface WelcomeScreenProps {
  onStart: () => void;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onStart }) => {
  const navigate = useNavigate();

  const handleStartGame = () => {
    onStart();
    navigate('/upload');
  };

  return (
    <Page
      className="welcome-screen"
      style={{
        backgroundImage: `url(${bgImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed'
      }}
    >
      {/* Selected Zone */}
      <Box
        className="selected-zone"
        style={{
          backgroundImage: `url(${selectZoneImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        {/* Progress Indicator */}
        <Box className="progress-indicator">
          <div className="progress-step active">1</div>
          <div className="progress-step">2</div>
          <div className="progress-step">3</div>
          <div className="progress-step">4</div>
        </Box>

        {/* Hero Section */}
        <Box className="hero-section">
          <Box className="game-logo">
            <div className="logo-placeholder">🎮</div>
          </Box>

          <Text.Title className="game-title">
            Trải nghiệm Game tương tác
          </Text.Title>

          <Text className="game-subtitle">
            Tải lên hình ảnh, trả lời câu hỏi và khám phá kết quả tuyệt vời!
          </Text>
        </Box>

        {/* Features Section */}
        <Box className="features-section">
          <Box className="feature-item">
            <div className="feature-icon">📸</div>
            <Text className="feature-text">Tải lên hình ảnh</Text>
          </Box>

          <Box className="feature-item">
            <div className="feature-icon">📝</div>
            <Text className="feature-text">Trả lời khảo sát</Text>
          </Box>

          <Box className="feature-item">
            <div className="feature-icon">🎬</div>
            <Text className="feature-text">Xem video thưởng</Text>
          </Box>

          <Box className="feature-item">
            <div className="feature-icon">🏆</div>
            <Text className="feature-text">Nhận kết quả</Text>
          </Box>
        </Box>

        <Box className="disclaimer-section">
          <Text className="disclaimer">
            Game này mất khoảng 5-10 phút để hoàn thành
          </Text>
        </Box>

        {/* Action Buttons */}
        <Box className="action-buttons">
          <Button
            variant="primary"
            size="large"
            fullWidth
            onClick={handleStartGame}
            className="start-button"
          >
            Chơi
          </Button>
        </Box>
      </Box>
    </Page>
  );
};

export default WelcomeScreen;
